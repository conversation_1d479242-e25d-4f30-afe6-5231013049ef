<div
    x-data="{
        currentIndex: {{ $currentIndex }},
        posts: @js($posts),
        isPlaying: true,
        isMuted: false,

        init() {
            this.$watch('currentIndex', (value) => {
                this.setupCurrentImage();
            });

            this.$nextTick(() => {
                this.setupCurrentImage();
                this.setupIntersectionObserver();

                // Rolar para o post inicial se especificado
                if (this.currentIndex > 0) {
                    this.scrollToImage(this.currentIndex);
                }
            });

            // Escutar eventos do Livewire
            Livewire.on('postLiked', (data) => {
                const postIndex = this.posts.findIndex(post => post.id === data.postId);
                if (postIndex !== -1) {
                    this.posts[postIndex].likes_count = data.likesCount;
                    this.posts[postIndex].liked_by_user = data.isLiked;
                }
            });

            Livewire.on('commentAdded', (data) => {
                const postIndex = this.posts.findIndex(post => post.id === data.postId);
                if (postIndex !== -1) {
                    this.posts[postIndex].comments.unshift(data.comment);
                    this.posts[postIndex].comments_count = data.commentsCount;
                }
            });
        },

        setupIntersectionObserver() {
            const images = document.querySelectorAll('.story-image');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const index = parseInt(entry.target.dataset.index);
                        if (this.currentIndex !== index) {
                            this.currentIndex = index;
                        }
                    }
                });
            }, { threshold: 0.7 });

            images.forEach(image => observer.observe(image));
        },

        setupCurrentImage() {
            // Função para configurar a imagem atual (se necessário)
            setTimeout(() => {
                const image = document.querySelector(`.story-image[data-index='${this.currentIndex}']`);
                if (image) {
                    // Pode adicionar lógica específica para imagens aqui
                }
            }, 100);
        },

        nextImage() {
            if (this.currentIndex < this.posts.length - 1) {
                this.currentIndex++;
                this.scrollToImage(this.currentIndex);
            }
        },

        prevImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
                this.scrollToImage(this.currentIndex);
            }
        },

        scrollToImage(index) {
            const imageElement = document.getElementById('story-' + this.posts[index].id);
            if (imageElement) {
                imageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        },

        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
    }"
    class="h-screen w-full bg-black overflow-hidden"
    @keydown.arrow-down.window="nextImage()"
    @keydown.arrow-up.window="prevImage()"
    @wheel.debounce.300="$event.deltaY > 0 ? nextImage() : prevImage()"
>
    <!-- Stories Container -->
    <div class="snap-y snap-mandatory h-screen w-full overflow-y-scroll">
        <template x-for="(post, index) in posts" :key="post.id">
            <div
                class="snap-start h-screen w-full relative flex items-center justify-center bg-black"
                :id="'story-' + post.id"
            >
                <!-- Image -->
                <img
                    :src="post.image"
                    class="story-image h-full w-full object-contain"
                    :data-index="index"
                    @click="nextImage()"
                />

                <!-- Overlay Content -->
                <div class="absolute inset-0 pointer-events-none">
                    <!-- Top Gradient -->
                    <div class="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-black/50 to-transparent"></div>
                    
                    <!-- Bottom Gradient -->
                    <div class="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>

                <!-- User Info (Top) -->
                <div class="absolute top-4 left-4 right-4 flex items-center justify-between z-10">
                    <div class="flex items-center space-x-3">
                        <img 
                            :src="post.user.avatar" 
                            :alt="post.user.name"
                            class="w-10 h-10 rounded-full border-2 border-white/20"
                        />
                        <div>
                            <p class="text-white font-semibold text-sm" x-text="post.user.name"></p>
                            <p class="text-white/70 text-xs" x-text="'@' + post.user.username"></p>
                        </div>
                    </div>
                    
                    <!-- View Toggle and Close Button -->
                    <div class="flex items-center space-x-3">
                        <a href="{{ route('feed_imagens_grid') }}"
                           class="text-white/70 hover:text-white transition-colors pointer-events-auto"
                           wire:navigate
                           title="Visualização em Grid">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                        </a>
                        <a href="{{ route('feed_imagens_grid') }}"
                           class="text-white/70 hover:text-white transition-colors pointer-events-auto"
                           wire:navigate
                           title="Fechar">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Content and Actions (Bottom) -->
                <div class="absolute bottom-0 left-0 right-0 p-4 z-10">
                    <!-- Post Content -->
                    <div class="mb-4" x-show="post.content">
                        <p class="text-white text-sm leading-relaxed" x-html="post.content"></p>
                    </div>

                    <!-- Actions Row -->
                    <div class="flex items-center justify-between mb-4">
                        <!-- Left Actions -->
                        <div class="flex items-center space-x-4">
                            <!-- Like Button -->
                            <button 
                                @click="$wire.toggleLike(post.id)"
                                class="flex items-center space-x-2 text-white/80 hover:text-white transition-colors pointer-events-auto"
                            >
                                <svg 
                                    class="w-6 h-6 transition-colors"
                                    :class="post.liked_by_user ? 'text-red-500 fill-current' : 'text-white'"
                                    fill="none" 
                                    stroke="currentColor" 
                                    viewBox="0 0 24 24"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                <span class="text-sm font-medium" x-text="formatNumber(post.likes_count)"></span>
                            </button>

                            <!-- Comment Button -->
                            <button 
                                @click="$refs['commentInput' + post.id].focus()"
                                class="flex items-center space-x-2 text-white/80 hover:text-white transition-colors pointer-events-auto"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <span class="text-sm font-medium" x-text="formatNumber(post.comments_count)"></span>
                            </button>
                        </div>

                        <!-- Right Actions -->
                        <div class="flex items-center space-x-3">
                            <!-- Share Button -->
                            <button class="text-white/80 hover:text-white transition-colors pointer-events-auto">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Comments Section -->
                    <div class="space-y-2 mb-4 max-h-32 overflow-y-auto" x-show="post.comments.length > 0">
                        <template x-for="comment in post.comments.slice(0, 3)" :key="comment.id">
                            <div class="flex items-start space-x-2">
                                <img 
                                    :src="comment.user.avatar" 
                                    :alt="comment.user.name"
                                    class="w-6 h-6 rounded-full flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <p class="text-white text-sm">
                                        <span class="font-medium" x-text="comment.user.name"></span>
                                        <span class="ml-2" x-html="comment.body"></span>
                                    </p>
                                    <p class="text-white/50 text-xs mt-1" x-text="comment.created_at"></p>
                                </div>
                            </div>
                        </template>
                    </div>

                    <!-- Comment Input -->
                    <div class="flex items-center space-x-3 pointer-events-auto">
                        <img 
                            src="{{ Auth::user()->userPhotos->first() ? Storage::url(Auth::user()->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}" 
                            alt="{{ Auth::user()->name }}"
                            class="w-8 h-8 rounded-full flex-shrink-0"
                        />
                        <div class="flex-1 relative">
                            <input 
                                :x-ref="'commentInput' + post.id"
                                type="text" 
                                placeholder="Adicione um comentário..."
                                class="w-full bg-white/10 border border-white/20 rounded-full px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:border-white/40 text-sm"
                                @keydown.enter="
                                    if ($event.target.value.trim()) {
                                        $wire.addComment(post.id, $event.target.value);
                                        $event.target.value = '';
                                    }
                                "
                                @focus="$wire.setActiveCommentPostId(post.id)"
                                @input="$wire.handleCommentInput(post.id, $event.target.value)"
                            />
                            
                            <!-- Autocomplete Suggestions -->
                            <div 
                                x-show="$wire.showHashtagSuggestions && $wire.activeCommentPostId === post.id" 
                                class="absolute bottom-full left-0 right-0 mb-2 bg-zinc-800 border border-zinc-600 rounded-lg shadow-lg max-h-40 overflow-y-auto z-50"
                            >
                                <template x-for="hashtag in $wire.hashtagSuggestions" :key="hashtag.id">
                                    <button 
                                        @click="$wire.selectHashtagForPost(post.id, $refs['commentInput' + post.id].value, hashtag.name)"
                                        class="w-full text-left px-3 py-2 text-white hover:bg-zinc-700 transition-colors text-sm"
                                    >
                                        <span class="text-blue-400">#</span><span x-text="hashtag.name"></span>
                                        <span class="text-zinc-400 ml-2" x-text="'(' + hashtag.posts_count + ' posts)'"></span>
                                    </button>
                                </template>
                            </div>

                            <div 
                                x-show="$wire.showMentionSuggestions && $wire.activeCommentPostId === post.id" 
                                class="absolute bottom-full left-0 right-0 mb-2 bg-zinc-800 border border-zinc-600 rounded-lg shadow-lg max-h-40 overflow-y-auto z-50"
                            >
                                <template x-for="user in $wire.mentionSuggestions" :key="user.id">
                                    <button 
                                        @click="$wire.selectMentionForPost(post.id, $refs['commentInput' + post.id].value, user.username)"
                                        class="w-full text-left px-3 py-2 text-white hover:bg-zinc-700 transition-colors text-sm flex items-center space-x-2"
                                    >
                                        <img :src="user.avatar" :alt="user.name" class="w-6 h-6 rounded-full">
                                        <div>
                                            <div class="font-medium" x-text="user.name"></div>
                                            <div class="text-zinc-400 text-xs" x-text="'@' + user.username"></div>
                                        </div>
                                    </button>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Indicators -->
                <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-2 z-10">
                    <template x-for="(_, i) in posts" :key="i">
                        <div 
                            class="w-1 h-8 rounded-full transition-all duration-300"
                            :class="i === currentIndex ? 'bg-white' : 'bg-white/30'"
                        ></div>
                    </template>
                </div>
            </div>
        </template>
    </div>

    <!-- Loading State -->
    <div x-show="posts.length === 0" class="h-screen w-full flex items-center justify-center bg-black">
        <div class="text-center">
            <svg class="animate-spin h-8 w-8 text-white mx-auto mb-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="text-white/70">Carregando imagens...</p>
        </div>
    </div>
</div>
